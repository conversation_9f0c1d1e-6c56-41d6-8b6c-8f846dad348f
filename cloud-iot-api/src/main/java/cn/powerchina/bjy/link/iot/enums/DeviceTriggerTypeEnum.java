package cn.powerchina.bjy.link.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * ThingModeTypeEnum
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum DeviceTriggerTypeEnum {

    ATTRIBUTE(1, "属性触发"),
    EVENT(2, "事件触发"),
    ON_OR_OFF(3, "上下线触发"),
    ;

    private final int type;
    private final String desc;

    /**
     * 根据类型获取描述
     *
     * @param type
     * @return
     */
    public static String getDescByType(Integer type) {
        for (DeviceTriggerTypeEnum modeType : DeviceTriggerTypeEnum.values()) {
            if (Objects.equals(type, modeType.type)) {
                return modeType.getDesc();
            }
        }
        return null;
    }
}
