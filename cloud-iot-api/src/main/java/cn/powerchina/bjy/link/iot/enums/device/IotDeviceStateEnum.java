package cn.powerchina.bjy.link.iot.enums.device;



import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * IoT 设备状态枚举
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum IotDeviceStateEnum  {

    INACTIVE(0, "未激活"),
    ONLINE(1, "在线"),
    OFFLINE(2, "离线");



    private final Integer state;
    /**
     * 状态名
     */
    private final String name;


    public static boolean isOnline(Integer state) {
        return ONLINE.getState().equals(state);
    }

}
