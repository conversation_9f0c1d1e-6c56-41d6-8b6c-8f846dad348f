package cn.powerchina.bjy.link.iot.enums;

/**
 * @Description: iot主题常量
 * @Author: yhx
 * @CreateDate: 2024/10/12
 */
public class IotTopicConstant {

    /**
     * 服务指令topic
     */
    public static final String TOPIC_DEVICE_COMMAND = "topic_device_command";

    /**
     * 服务指令结果topic
     */
    public static final String TOPIC_DEVICE_COMMAND_RESULT = "topic_device_command_result";

    /**
     * 属性指令topic
     */
    public static final String TOPIC_DEVICE_PROPERTY = "topic_device_property";

    /**
     * 属性指令结果topic
     */
    public static final String TOPIC_DEVICE_PROPERTY_RESULT = "topic_device_property_result";

    /**
     * 事件topic
     */
    public static final String TOPIC_DEVICE_EVENT = "topic_device_event";

    /**
     * 在线离线topic
     */
    public static final String TOPIC_DEVICE_ONLINE = "topic_device_online";

    /**
     * 在线离线结果topic
     */
    public static final String TOPIC_DEVICE_ONLINE_RESULT = "topic_device_online_result";

    /**
     * 配置topic
     */
    public static final String TOPIC_DEVICE_CONFIG = "topic_device_config";

    /**
     * 配置结果topic
     */
    public static final String TOPIC_DEVICE_CONFIG_RESULT = "topic_device_config_result";

    /**
     * 同步topic
     */
    public static final String TOPIC_DEVICE_SYNC = "topic_device_sync";

    /**
     * 上报的设备采集数据topic
     */
    public static final String TOPIC_DEVICE_COLLECT_DATA = "topic_device_collect_data";

    /**
     * 设备分组变更
     */
    public static final String TOPIC_DEVICE_GROUP_CHANGE = "topic_device_group_change";

    /**
     * 设备状态变更
     */
    public static final String TOPIC_DEVICE_STATUS_CHANGE = "topic_device_status_change";

    /**
     * 大坝下发采集指令
     */
    public static final String TOPIC_DAM_COLLECT_COMMAND = "topic_dam_collect_command";

    /**
     * 大坝下发时钟同步指令
     */
    public static final String TOPIC_DAM_CLOCK_SYNC_COMMAND = "topic_dam_clock_sync_command";
    public static final String GROUP_DAM_CLOCK_SYNC_COMMAND = "group_dam_clock_sync_command";

    /**
     * iot上报时钟同步数据
     */
    public static final String TOPIC_DEVICE_CLOCK_SYNC_DATA = "topic_device_clock_sync_data";
    public static final String GROUP_DEVICE_CLOCK_SYNC_DATA = "group_device_clock_sync_data";


    /**
     * 执行动作延迟队列
     */
    public static final String TOPIC_RULE_ACTION_DELAY = "topic_rule_action_delay";
    public static final String GROUP_RULE_ACTION_DELAY = "group_rule_action_delay";

    /**
     * 设备上报的数据（测试）
     */
    public static final String TOPIC_DEVICE_DATA = "topic_device_data";
    public static final String TOPIC_DEVICE_CHANGE = "topic_device_change";
    public static final String GROUP_DEVICE_CHANGE = "group_device_change";
    /**
     * 下发指令
     */
    public static final String TOPIC_DEVICE_CMD = "topic_device_cmd";
    public static final String GROUP_DEVICE_CMD = "group_device_cmd";

    // ------------------------ 消费组常量 ------------------------
    /**
     * 服务指令消费组group
     */
    public static final String GROUP_DEVICE_COMMAND = "group_device_command";
    /**
     * 服务指令结果消费组
     */
    public static final String GROUP_DEVICE_COMMAND_RESULT = "group_device_command_result";
    /**
     * 属性指令消费组
     */
    public static final String GROUP_DEVICE_PROPERTY = "group_device_property";
    /**
     * 属性指令结果消费组
     */
    public static final String GROUP_DEVICE_PROPERTY_RESULT = "group_device_property_result";
    /**
     * 事件消费组
     */
    public static final String GROUP_DEVICE_EVENT = "group_device_event";
    /**
     * 在线离线消费组
     */
    public static final String GROUP_DEVICE_ONLINE = "group_device_online";
    /**
     * 在线离线结果消费组
     */
    public static final String GROUP_DEVICE_ONLINE_RESULT = "group_device_online_result";
    /**
     * 配置消费组
     */
    public static final String GROUP_DEVICE_CONFIG = "group_device_config";
    /**
     * 配置结果消费组
     */
    public static final String GROUP_DEVICE_CONFIG_RESULT = "group_device_config_result";
    /**
     * 同步消费组
     */
    public static final String GROUP_DEVICE_SYNC = "group_device_sync";
    /**
     * 上报的设备采集数据消费组
     */
    public static final String GROUP_DEVICE_COLLECT_DATA = "group_device_collect_data";
    /**
     * 设备分组变更消费组
     */
    public static final String GROUP_DEVICE_GROUP_CHANGE = "group_device_group_change";
    /**
     * 设备状态变更消费组
     */
    public static final String GROUP_DEVICE_STATUS_CHANGE = "group_device_status_change";
    /**
     * 大坝下发采集指令消费组
     */
    public static final String GROUP_DAM_COLLECT_COMMAND = "group_dam_collect_command";

}
