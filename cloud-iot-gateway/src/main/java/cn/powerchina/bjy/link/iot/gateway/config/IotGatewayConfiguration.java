package cn.powerchina.bjy.link.iot.gateway.config;


import cn.powerchina.bjy.link.iot.api.device.auth.IotDeviceCommonApi;
import cn.powerchina.bjy.link.iot.gateway.protocol.emqx.IotEmqxAuthEventProtocol;
import cn.powerchina.bjy.link.iot.gateway.protocol.emqx.IotEmqxDownstreamSubscriber;
import cn.powerchina.bjy.link.iot.gateway.protocol.emqx.IotEmqxUpstreamProtocol;
import cn.powerchina.bjy.link.iot.gateway.protocol.http.IotHttpDownstreamSubscriber;
import cn.powerchina.bjy.link.iot.gateway.protocol.http.IotHttpUpstreamProtocol;
import cn.powerchina.bjy.link.iot.gateway.protocol.tcp.tcp.IotTcpConnectionManager;
import cn.powerchina.bjy.link.iot.gateway.protocol.tcp.tcp.IotTcpDownstreamSubscriber;
import cn.powerchina.bjy.link.iot.gateway.protocol.tcp.tcp.IotTcpUpstreamProtocol;
import cn.powerchina.bjy.link.iot.gateway.protocol.tcp.tcp.router.IotTcpDownstreamHandler;
import cn.powerchina.bjy.link.iot.gateway.service.device.IotDeviceService;
import cn.powerchina.bjy.link.iot.gateway.service.device.message.IotDeviceMessageService;
import io.vertx.core.Vertx;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties(IotGatewayProperties.class)
@Slf4j
public class IotGatewayConfiguration {

    /**
     * IoT 网关 HTTP 协议配置类
     */
    @Configuration
    @ConditionalOnProperty(prefix = "cloud.iot.gateway.protocol.http", name = "enabled", havingValue = "true")
    @Slf4j
    public static class HttpProtocolConfiguration {

        @Bean
        public IotHttpUpstreamProtocol iotHttpUpstreamProtocol(IotGatewayProperties gatewayProperties) {
            return new IotHttpUpstreamProtocol(gatewayProperties.getProtocol().getHttp());
        }

        @Bean
        public IotHttpDownstreamSubscriber iotHttpDownstreamSubscriber(IotHttpUpstreamProtocol httpUpstreamProtocol) {
            return new IotHttpDownstreamSubscriber(httpUpstreamProtocol);
        }
    }

    /**
     * IoT 网关 EMQX 协议配置类
     */
    @Configuration
    @ConditionalOnProperty(prefix = "cloud.iot.gateway.protocol.emqx", name = "enabled", havingValue = "true")
    @Slf4j
    public static class MqttProtocolConfiguration {


        @Bean
        public IotEmqxAuthEventProtocol iotEmqxAuthEventProtocol(IotGatewayProperties gatewayProperties) {
            return new IotEmqxAuthEventProtocol(gatewayProperties.getProtocol().getEmqx());
        }

        @Bean
        public IotEmqxUpstreamProtocol iotEmqxUpstreamProtocol(IotGatewayProperties gatewayProperties) {
            return new IotEmqxUpstreamProtocol(gatewayProperties.getProtocol().getEmqx());
        }

        @Bean
        public IotEmqxDownstreamSubscriber iotEmqxDownstreamSubscriber(IotEmqxUpstreamProtocol mqttUpstreamProtocol) {
            return new IotEmqxDownstreamSubscriber(mqttUpstreamProtocol);
        }

    }


    /**
     * IoT 网关 TCP 协议配置类
     */
    @Configuration
    @ConditionalOnProperty(prefix = "cloud.iot.gateway.protocol.tcp", name = "enabled", havingValue = "true")
    @Slf4j
    public static class TcpProtocolConfiguration {

        @Bean(destroyMethod = "close")
        public Vertx tcpVertx() {
            return Vertx.vertx();
        }

        @Bean
        public IotTcpUpstreamProtocol iotTcpUpstreamProtocol(Vertx tcpVertx, IotGatewayProperties gatewayProperties,
                                                             IotTcpConnectionManager connectionManager,
                                                             IotDeviceMessageService messageService,
                                                             IotDeviceService deviceService, IotDeviceCommonApi deviceApi) {
            return new IotTcpUpstreamProtocol(tcpVertx, gatewayProperties, connectionManager,
                    messageService, deviceService, deviceApi);
        }

        @Bean
        public IotTcpDownstreamSubscriber iotTcpDownstreamSubscriber(IotTcpUpstreamProtocol tcpUpstreamProtocol,
                                                                     IotTcpDownstreamHandler downstreamHandler) {
            return new IotTcpDownstreamSubscriber(tcpUpstreamProtocol, downstreamHandler);
        }

    }

}
