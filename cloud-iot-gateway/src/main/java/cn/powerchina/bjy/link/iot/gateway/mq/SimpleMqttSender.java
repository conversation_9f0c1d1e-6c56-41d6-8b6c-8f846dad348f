package cn.powerchina.bjy.link.iot.gateway.mq;


import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

/**
 * 简单的 MQTT 消息发送工具
 */
public class SimpleMqttSender {

    // MQTT 服务器配置（根据实际环境修改）
    private static final String BROKER = "tcp://**********:2883"; // 服务器地址+端口（EMQX默认1883）
    private static final String CLIENT_ID = "iot-gateway-mqtt"; // 客户端唯一ID
    private static final String TOPIC = "/system/test"; // 发送的主题
    private static final String USERNAME = "admin"; // 用户名（EMQX默认admin）
    private static final String PASSWORD = "adminpublic#"; // 密码（EMQX默认public）

    public static void main(String[] args) {
        MqttClient client = null;
        try {
            // 1. 创建客户端（使用内存持久化，不存储本地文件）
            MemoryPersistence persistence = new MemoryPersistence();
            client = new MqttClient(BROKER, CLIENT_ID, persistence);

            // 2. 配置连接参数
            MqttConnectOptions connOpts = new MqttConnectOptions();
            connOpts.setCleanSession(true); // 连接后不保留历史会话
            connOpts.setUserName(USERNAME); // 设置用户名
            connOpts.setPassword(PASSWORD.toCharArray()); // 设置密码
            connOpts.setConnectionTimeout(10); // 连接超时（秒）
            connOpts.setKeepAliveInterval(60); // 心跳间隔（秒）

            // 3. 连接服务器
            System.out.println("连接 MQTT 服务器: " + BROKER);
            client.connect(connOpts);
            System.out.println("连接成功！");

            // 4. 准备要发送的消息
            String messageContent = "Hello MQTT! 这是一条测试消息 " + System.currentTimeMillis();
            MqttMessage message = new MqttMessage(messageContent.getBytes());
            message.setQos(1); // 消息质量：1（至少一次送达）
            message.setRetained(false); // 不保留消息

            // 5. 发送消息到指定主题
            System.out.println("发送消息到主题 [" + TOPIC + "]: " + messageContent);
            client.publish(TOPIC, message);

        } catch (MqttException e) {
            System.err.println("MQTT 操作失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 6. 断开连接（无论成功失败都要清理资源）
            if (client != null && client.isConnected()) {
                try {
                    client.disconnect();
                    System.out.println("已断开连接");
                } catch (MqttException e) {
                    System.err.println("断开连接失败: " + e.getMessage());
                }
            }
        }
    }
}