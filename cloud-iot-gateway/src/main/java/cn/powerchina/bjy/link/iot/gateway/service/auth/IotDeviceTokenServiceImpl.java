package cn.powerchina.bjy.link.iot.gateway.service.auth;

import cn.hutool.core.date.TemporalAccessorUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import cn.powerchina.bjy.cloud.framework.common.util.date.LocalDateTimeUtils;
import cn.powerchina.bjy.link.iot.gateway.config.IotGatewayProperties;
import cn.powerchina.bjy.link.iot.gateway.util.IotDeviceAuthUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.DateTimeException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.gateway.enums.ErrorCodeConstants.DEVICE_TOKEN_EXPIRED;


/**
 * IoT 设备 Token Service 实现类：调用远程的 device http 接口，进行设备 Token 生成、解析等逻辑
 *
 * 注意：目前仅 HTTP 协议使用
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class IotDeviceTokenServiceImpl implements IotDeviceTokenService {

    @Resource
    private IotGatewayProperties gatewayProperties;

    @Override
    public String createToken(String productKey, String deviceName) {
        Assert.notBlank(productKey, "productKey 不能为空");
        Assert.notBlank(deviceName, "deviceName 不能为空");
        // 构建 JWT payload
        Map<String, Object> payload = new HashMap<>();
        payload.put("productKey", productKey);
        payload.put("deviceName", deviceName);
        LocalDateTime expireTime = LocalDateTimeUtils.addTime(gatewayProperties.getToken().getExpiration());
        payload.put("exp", toEpochSecond(expireTime)); // 过期时间（exp 是 JWT 规范推荐）

        // 生成 JWT Token
        return JWTUtil.createToken(payload, gatewayProperties.getToken().getSecret().getBytes());
    }
    /**
     * 将给定的 {@link LocalDateTime} 转换为自 Unix 纪元时间（1970-01-01T00:00:00Z）以来的秒数。
     *
     * @param sourceDateTime 需要转换的本地日期时间，不能为空
     * @return 自 1970-01-01T00:00:00Z 起的秒数（epoch second）
     * @throws NullPointerException 如果 {@code sourceDateTime} 为 {@code null}
     * @throws DateTimeException 如果转换过程中发生时间超出范围或其他时间处理异常
     */
    public static Long toEpochSecond(LocalDateTime sourceDateTime) {
        return TemporalAccessorUtil.toInstant(sourceDateTime).getEpochSecond();
    }
    @Override
    public IotDeviceAuthUtils.DeviceInfo verifyToken(String token) {
        Assert.notBlank(token, "token 不能为空");
        // 校验 JWT Token
        boolean verify = JWTUtil.verify(token, gatewayProperties.getToken().getSecret().getBytes());
        if (!verify) {
            throw exception(DEVICE_TOKEN_EXPIRED);
        }

        // 解析 Token
        JWT jwt = JWTUtil.parseToken(token);
        JSONObject payload = jwt.getPayloads();
        // 检查过期时间
        Long exp = payload.getLong("exp");
        if (exp == null || exp < System.currentTimeMillis() / 1000) {
            throw exception(DEVICE_TOKEN_EXPIRED);
        }
        String productKey = payload.getStr("productKey");
        String deviceName = payload.getStr("deviceName");
        Assert.notBlank(productKey, "productKey 不能为空");
        Assert.notBlank(deviceName, "deviceName 不能为空");
        return new IotDeviceAuthUtils.DeviceInfo().setProductKey(productKey).setDeviceName(deviceName);
    }

    @Override
    public IotDeviceAuthUtils.DeviceInfo parseUsername(String username) {
        return IotDeviceAuthUtils.parseUsername(username);
    }

}
