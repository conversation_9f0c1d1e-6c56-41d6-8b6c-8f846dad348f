package cn.powerchina.bjy.link.iot.gateway.enums;


import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * IoT 设备状态枚举
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum IotDeviceStateEnum  {

    INACTIVE(0, "未激活"),
    ONLINE(1, "在线"),
    OFFLINE(2, "离线");

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(IotDeviceStateEnum::getState).toArray(Integer[]::new);

    /**
     * 状态
     */
    private final Integer state;
    /**
     * 状态名
     */
    private final String name;


    public static boolean isOnline(Integer state) {
        return ONLINE.getState().equals(state);
    }

}
