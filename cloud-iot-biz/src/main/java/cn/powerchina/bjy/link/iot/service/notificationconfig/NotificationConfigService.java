package cn.powerchina.bjy.link.iot.service.notificationconfig;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo.NotificationConfigPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo.NotificationConfigSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationconfig.NotificationConfigDO;
import jakarta.validation.*;

import java.util.List;

/**
 * 消息配置 Service 接口
 *
 * <AUTHOR>
 */
public interface NotificationConfigService {

    /**
     * 创建消息配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createNotificationConfig(@Valid NotificationConfigSaveReqVO createReqVO);

    /**
     * 更新消息配置
     *
     * @param updateReqVO 更新信息
     */
    void updateNotificationConfig(@Valid NotificationConfigSaveReqVO updateReqVO);

    /**
     * 删除消息配置
     *
     * @param id 编号
     */
    void deleteNotificationConfig(Long id);

    /**
     * 获得消息配置
     *
     * @param id 编号
     * @return 消息配置
     */
    NotificationConfigDO getNotificationConfig(Long id);

    /**
     * 获得消息配置分页
     *
     * @param pageReqVO 分页查询
     * @return 消息配置分页
     */
    PageResult<NotificationConfigDO> getNotificationConfigPage(NotificationConfigPageReqVO pageReqVO);

    List<NotificationConfigDO> getNotificationConfigList();

}