package cn.powerchina.bjy.link.iot.service.notificationrecord;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationrecord.NotificationRecordDO;
import jakarta.validation.*;

/**
 * 消息记录 Service 接口
 *
 * <AUTHOR>
 */
public interface NotificationRecordService {

    /**
     * 创建消息记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createNotificationRecord(@Valid NotificationRecordSaveReqVO createReqVO);

    /**
     * 更新消息记录
     *
     * @param updateReqVO 更新信息
     */
    void updateNotificationRecord(@Valid NotificationRecordSaveReqVO updateReqVO);

    /**
     * 删除消息记录
     *
     * @param id 编号
     */
    void deleteNotificationRecord(Long id);

    /**
     * 获得消息记录
     *
     * @param id 编号
     * @return 消息记录
     */
    NotificationRecordDO getNotificationRecord(Long id);

    /**
     * 获得消息记录分页
     *
     * @param pageReqVO 分页查询
     * @return 消息记录分页
     */
    PageResult<NotificationRecordDO> getNotificationRecordPage(NotificationRecordPageReqVO pageReqVO);

}