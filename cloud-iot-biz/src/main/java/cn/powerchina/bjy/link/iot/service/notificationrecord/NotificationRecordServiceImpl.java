package cn.powerchina.bjy.link.iot.service.notificationrecord;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationrecord.NotificationRecordDO;
import cn.powerchina.bjy.link.iot.dal.mysql.notificationrecord.NotificationRecordMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.NOTIFICATION_RECORD_NOT_EXISTS;

/**
 * 消息记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NotificationRecordServiceImpl implements NotificationRecordService {

    @Resource
    private NotificationRecordMapper notificationRecordMapper;

    @Override
    public Long createNotificationRecord(NotificationRecordSaveReqVO createReqVO) {
        // 插入
        NotificationRecordDO notificationRecord = BeanUtils.toBean(createReqVO, NotificationRecordDO.class);
        notificationRecordMapper.insert(notificationRecord);
        // 返回
        return notificationRecord.getId();
    }

    @Override
    public void updateNotificationRecord(NotificationRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateNotificationRecordExists(updateReqVO.getId());
        // 更新
        NotificationRecordDO updateObj = BeanUtils.toBean(updateReqVO, NotificationRecordDO.class);
        notificationRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteNotificationRecord(Long id) {
        // 校验存在
        validateNotificationRecordExists(id);
        // 删除
        notificationRecordMapper.deleteById(id);
    }

    private void validateNotificationRecordExists(Long id) {
        if (notificationRecordMapper.selectById(id) == null) {
            throw exception(NOTIFICATION_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public NotificationRecordDO getNotificationRecord(Long id) {
        return notificationRecordMapper.selectById(id);
    }

    @Override
    public PageResult<NotificationRecordDO> getNotificationRecordPage(NotificationRecordPageReqVO pageReqVO) {
        return notificationRecordMapper.selectPage(pageReqVO);
    }

}