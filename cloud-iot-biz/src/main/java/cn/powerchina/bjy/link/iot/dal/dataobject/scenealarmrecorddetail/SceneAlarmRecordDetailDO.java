package cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecorddetail;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 告警记录详情 DO
 *
 * <AUTHOR>
 */
@TableName("iot_scene_alarm_record_detail")
@KeySequence("iot_scene_alarm_record_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneAlarmRecordDetailDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 告警ID
     */
    private Long alarmId;
    /**
     * 规则ID
     */
    private Long ruleId;
    /**
     * 告警时间
     */
    private LocalDateTime alarmTime;
    /**
     * 触发设备
     */
    private String deviceName;
    /**
     * 触发条件
     */
    private String triggerCondition;

}