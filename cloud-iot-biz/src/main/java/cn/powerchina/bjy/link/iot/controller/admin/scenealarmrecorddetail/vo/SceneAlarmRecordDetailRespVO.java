package cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 告警记录详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SceneAlarmRecordDetailRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31816")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "告警id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31816")
    @ExcelProperty("告警id")
    private Long alarmId;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31425")
    @ExcelProperty("规则ID")
    private Long ruleId;

    @Schema(description = "告警时间")
    @ExcelProperty("告警时间")
    private LocalDateTime alarmTime;

    @Schema(description = "触发设备")
    @ExcelProperty("触发设备")
    private String deviceName;

    @Schema(description = "触发条件")
    @ExcelProperty("触发设备")
    private String triggerCondition;

    @Schema(description = "属性值")
    @ExcelProperty("属性值")
    private String attributeValue;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}