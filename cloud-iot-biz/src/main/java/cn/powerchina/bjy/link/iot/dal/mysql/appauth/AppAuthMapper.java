package cn.powerchina.bjy.link.iot.dal.mysql.appauth;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.appauth.vo.AppAuthPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.appauth.AppAuthDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 应用管理认证 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AppAuthMapper extends BaseMapperX<AppAuthDO> {

    default PageResult<AppAuthDO> selectPage(AppAuthPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AppAuthDO>()
                .likeIfPresent(AppAuthDO::getAppName, reqVO.getAppName())
                .eqIfPresent(AppAuthDO::getAppCode, reqVO.getAppCode())
                .eqIfPresent(AppAuthDO::getSecret, reqVO.getSecret())
                .eqIfPresent(AppAuthDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(AppAuthDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AppAuthDO::getId));
    }

}