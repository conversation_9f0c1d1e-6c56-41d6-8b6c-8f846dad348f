package cn.powerchina.bjy.link.iot.dal.dataobject.transporttarget;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 转发规则-转发目标 DO
 *
 * <AUTHOR>
 */
@TableName("iot_transport_target")
@KeySequence("iot_transport_target_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransportTargetDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 规则id
     */
    private Long ruleId;
    /**
     * 目标名称
     */
    private String name;
    /**
     * 产品code
     */
    private String productCode;
    /**
     * 设备code
     */
    private String deviceCode;
    /**
     * 转发类型(1:HTTP推送 2MQTT推送)
     */
    private Integer transportType;
    /**
     * url地址
     */
    private String urlAddress;
    /**
     * token
     */
    private String token;
    /**
     * 转发topic
     */
    private String topic;

}