package cn.powerchina.bjy.link.iot.dal.mysql.notificationconfig;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo.NotificationConfigPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationconfig.NotificationConfigDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 消息配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NotificationConfigMapper extends BaseMapperX<NotificationConfigDO> {

    default PageResult<NotificationConfigDO> selectPage(NotificationConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NotificationConfigDO>()
                .likeIfPresent(NotificationConfigDO::getNotificationName, reqVO.getNotificationName())
                .eqIfPresent(NotificationConfigDO::getNotificationMethod, reqVO.getNotificationMethod())
                .eqIfPresent(NotificationConfigDO::getNotificationHost, reqVO.getNotificationHost())
                .eqIfPresent(NotificationConfigDO::getNotificationPort, reqVO.getNotificationPort())
                .eqIfPresent(NotificationConfigDO::getNotificationPass, reqVO.getNotificationPass())
                .likeIfPresent(NotificationConfigDO::getUsername, reqVO.getUsername())
                .eqIfPresent(NotificationConfigDO::getPassword, reqVO.getPassword())
                .betweenIfPresent(NotificationConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(NotificationConfigDO::getId));
    }

}