package cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ProductModelRelayVO {
    private Long id;

    private String productCode;
    //物模型标识符
    private String thingIdentity;

    private String thingName;

    private Integer thingType;

    private String datatype;

    private Integer readWriteType;

    private Integer eventType;

    private String inputParams;

    private String outputParams;

    private String extra;

    private String remark;

    private LocalDateTime createTime;

}