package cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 告警记录详情新增/修改 Request VO")
@Data
public class SceneAlarmRecordDetailSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31816")
    private Long id;

    @Schema(description = "告警id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31816")
    private Long alarmId;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31425")
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;

    @Schema(description = "告警时间")
    private LocalDateTime alarmTime;

    @Schema(description = "触发设备")
    private String deviceName;

    @Schema(description = "触发条件")
    private String triggerCondition;

    @Schema(description = "属性值")
    private String attributeValue;

}