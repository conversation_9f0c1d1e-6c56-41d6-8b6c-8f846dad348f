package cn.powerchina.bjy.link.iot.service.scenealarmrecorddetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo.SceneAlarmRecordDetailPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo.SceneAlarmRecordDetailSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecorddetail.SceneAlarmRecordDetailDO;
import cn.powerchina.bjy.link.iot.dal.mysql.scenealarmrecorddetail.SceneAlarmRecordDetailMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.SCENE_ALARM_RECORD_DETAIL_NOT_EXISTS;


/**
 * 告警记录详情 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SceneAlarmRecordDetailServiceImpl implements SceneAlarmRecordDetailService {

    @Resource
    private SceneAlarmRecordDetailMapper sceneAlarmRecordDetailMapper;

    @Override
    public Long createSceneAlarmRecordDetail(SceneAlarmRecordDetailSaveReqVO createReqVO) {
        // 插入
        SceneAlarmRecordDetailDO sceneAlarmRecordDetail = BeanUtils.toBean(createReqVO, SceneAlarmRecordDetailDO.class);
        sceneAlarmRecordDetailMapper.insert(sceneAlarmRecordDetail);
        // 返回
        return sceneAlarmRecordDetail.getId();
    }

    @Override
    public void updateSceneAlarmRecordDetail(SceneAlarmRecordDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateSceneAlarmRecordDetailExists(updateReqVO.getId());
        // 更新
        SceneAlarmRecordDetailDO updateObj = BeanUtils.toBean(updateReqVO, SceneAlarmRecordDetailDO.class);
        sceneAlarmRecordDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteSceneAlarmRecordDetail(Long id) {
        // 校验存在
        validateSceneAlarmRecordDetailExists(id);
        // 删除
        sceneAlarmRecordDetailMapper.deleteById(id);
    }

    private void validateSceneAlarmRecordDetailExists(Long id) {
        if (sceneAlarmRecordDetailMapper.selectById(id) == null) {
            throw exception(SCENE_ALARM_RECORD_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public SceneAlarmRecordDetailDO getSceneAlarmRecordDetail(Long id) {
        return sceneAlarmRecordDetailMapper.selectById(id);
    }

    @Override
    public PageResult<SceneAlarmRecordDetailDO> getSceneAlarmRecordDetailPage(SceneAlarmRecordDetailPageReqVO pageReqVO) {
        return sceneAlarmRecordDetailMapper.selectPage(pageReqVO);
    }

}