package cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 消息配置新增/修改 Request VO")
@Data
public class NotificationConfigSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7663")
    private Long id;

    @Schema(description = "配置名称", example = "赵六")
    private String notificationName;

    @Schema(description = "消息类型（1:钉钉 2：邮件 3：短信）")
    private Integer notificationMethod;

    @Schema(description = "服务器地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "服务器地址不能为空")
    private String notificationHost;

    @Schema(description = "端口")
    private String notificationPort;

    @Schema(description = "授权码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "授权码不能为空")
    private String notificationPass;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "用户名不能为空")
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "密码不能为空")
    private String password;

}