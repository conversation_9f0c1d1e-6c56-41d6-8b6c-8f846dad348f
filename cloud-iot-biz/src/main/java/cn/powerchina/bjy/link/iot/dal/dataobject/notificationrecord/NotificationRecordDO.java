package cn.powerchina.bjy.link.iot.dal.dataobject.notificationrecord;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 消息记录 DO
 *
 * <AUTHOR>
 */
@TableName("iot_notification_record")
@KeySequence("iot_notification_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationRecordDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 报警id
     */
    private Long alarmId;
    /**
     * 主题
     */
    private String subject;
    /**
     * 收件箱
     */
    private String inbox;
    /**
     * 发送状态，1成功 0失败
     */
    private Boolean sendStatus;
    /**
     * 发件箱
     */
    private String outbox;
    /**
     * 发送时间
     */
    private LocalDateTime sendTime;
    /**
     * 邮件内容
     */
    private String emailContent;
    /**
     * 备注
     */
    private String remark;

}