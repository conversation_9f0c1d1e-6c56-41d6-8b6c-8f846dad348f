package cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 消息配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NotificationConfigPageReqVO extends PageParam {

    @Schema(description = "配置名称", example = "赵六")
    private String notificationName;

    @Schema(description = "消息类型（1:钉钉 2：邮件 3：短信）")
    private Integer notificationMethod;

    @Schema(description = "服务器地址")
    private String notificationHost;

    @Schema(description = "端口")
    private String notificationPort;

    @Schema(description = "授权码")
    private String notificationPass;

    @Schema(description = "用户名", example = "芋艿")
    private String username;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}