package cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 转发规则-转发目标分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransportTargetPageReqVO extends PageParam {

    @Schema(description = "规则id", example = "26611")
    private Long ruleId;

    @Schema(description = "目标名称", example = "李四")
    private String name;

    @Schema(description = "产品code")
    private String productCode;

    @Schema(description = "设备code")
    private String deviceCode;

    @Schema(description = "转发类型(1:HTTP推送 2MQTT推送)", example = "2")
    private Integer transportType;

    @Schema(description = "url地址")
    private String urlAddress;

    @Schema(description = "token")
    private String token;

    @Schema(description = "转发topic")
    private String topic;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}