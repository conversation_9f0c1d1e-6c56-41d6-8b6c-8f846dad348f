package cn.powerchina.bjy.link.iot.aop.resourcespace;

import cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RolePageRespDTO;
import cn.powerchina.bjy.cloud.system.enums.permission.DataScopeEnum;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpacePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.roledatapermissions.RoleDataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.mysql.datapermissions.DataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.roledatapermissions.RoleDataPermissionsMapper;
import jakarta.annotation.Resource;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Aspect
@Component
public class ResourceSpacePermissionAspect {

    @Resource
    private RoleCommon roleCommon;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private RoleDataPermissionsMapper roleDataPermissionsMapper;

    @Resource
    private DataPermissionsMapper dataPermissionsMapper;


    @Around("@annotation(resourceSpacePermissionCheck)")
    public Object checkEdgeGatewayPermission(ProceedingJoinPoint joinPoint, ResourceSpacePermissionCheck resourceSpacePermissionCheck) throws Throwable {
        //获取方法参数
        Object[] args = joinPoint.getArgs();
        ResourceSpacePageReqVO pageReqVO = (ResourceSpacePageReqVO) args[0];

        //检查是否是超级管理员
        boolean superAdmin = roleCommon.checkIfSuperAdmin();
        if (!superAdmin) {
            //处理产品数据权限
            List<Long> ids = processDataPermissions();
            if (CollectionUtils.isEmpty(ids)) {
                return new PageResult<>(Collections.emptyList(), 0L);
            }
            pageReqVO.setIds(ids);
        }
        //继续执行原方法
        return joinPoint.proceed(args);
    }

    private List<Long> processDataPermissions() {

        CommonResult<List<RolePageRespDTO>> result = permissionApi.getPermissionRoleByUserId(getLoginUserId());
        List<Long> ids = new ArrayList<>();
        //判断响应是否成功
        if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(result.getCode())) {
            List<RolePageRespDTO> rolePageRespDTOList = result.getData();
            rolePageRespDTOList.forEach(role -> {
                //查询用户角色信息
                RoleDataPermissionsDO roleDataPermissionsDO = roleDataPermissionsMapper.selectAllByRoleId(role.getId());
                if (roleDataPermissionsDO != null) {
                    //判断是否是指定数据权限
                    if (DataScopeEnum.DEPT_CUSTOM.getScope().equals(roleDataPermissionsDO.getDataScope())) {
                        List<DataPermissionsDO> doList = dataPermissionsMapper.selectListByRoleId(roleDataPermissionsDO.getRoleId(), 1);
                        if (!CollectionUtils.isEmpty(doList)) {
                            doList.stream().forEach(item -> ids.add(Long.parseLong(item.getDataId())));
                        }
                    }
                } else {
                    ids.add(-1L);
                }
            });
        }
        return ids;
    }

}