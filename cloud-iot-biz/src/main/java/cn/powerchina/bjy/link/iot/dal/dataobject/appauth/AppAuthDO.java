package cn.powerchina.bjy.link.iot.dal.dataobject.appauth;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 应用管理认证 DO
 *
 * <AUTHOR>
 */
@TableName("iot_app_auth")
@KeySequence("iot_app_auth_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppAuthDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 应用code
     */
    private String appCode;
    /**
     * 密钥
     */
    private String secret;
    /**
     * 应用状态：启用/禁用
     */
    private Integer status;

}