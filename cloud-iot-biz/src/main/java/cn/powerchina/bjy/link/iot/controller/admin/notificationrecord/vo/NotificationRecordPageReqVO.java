package cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 消息记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NotificationRecordPageReqVO extends PageParam {

    @Schema(description = "告警id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long alarmId;

    @Schema(description = "主题")
    private String subject;

    @Schema(description = "收件箱")
    private String inbox;

    @Schema(description = "发送状态，1成功 0失败", example = "1")
    private Boolean sendStatus;

    @Schema(description = "发件箱")
    private String outbox;

    @Schema(description = "发送时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] sendTime;

    @Schema(description = "邮件内容")
    private String emailContent;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}