package cn.powerchina.bjy.link.iot.service.scenealarmrecorddetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo.SceneAlarmRecordDetailPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo.SceneAlarmRecordDetailSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecorddetail.SceneAlarmRecordDetailDO;
import jakarta.validation.*;

/**
 * 告警记录详情 Service 接口
 *
 * <AUTHOR>
 */
public interface SceneAlarmRecordDetailService {

    /**
     * 创建告警记录详情
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSceneAlarmRecordDetail(@Valid SceneAlarmRecordDetailSaveReqVO createReqVO);

    /**
     * 更新告警记录详情
     *
     * @param updateReqVO 更新信息
     */
    void updateSceneAlarmRecordDetail(@Valid SceneAlarmRecordDetailSaveReqVO updateReqVO);

    /**
     * 删除告警记录详情
     *
     * @param id 编号
     */
    void deleteSceneAlarmRecordDetail(Long id);

    /**
     * 获得告警记录详情
     *
     * @param id 编号
     * @return 告警记录详情
     */
    SceneAlarmRecordDetailDO getSceneAlarmRecordDetail(Long id);

    /**
     * 获得告警记录详情分页
     *
     * @param pageReqVO 分页查询
     * @return 告警记录详情分页
     */
    PageResult<SceneAlarmRecordDetailDO> getSceneAlarmRecordDetailPage(SceneAlarmRecordDetailPageReqVO pageReqVO);

}