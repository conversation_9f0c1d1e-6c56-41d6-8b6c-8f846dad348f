package cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 转发规则-数据源分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TransportSourcePageReqVO extends PageParam {

    @Schema(description = "规则id", example = "27450")
    private Long ruleId;

    @Schema(description = "数据类型，逗号分隔", example = "1")
    private String dataType;

    @Schema(description = "资源空间id：全部是-1", example = "29505")
    private Long resourceSpaceId;

    @Schema(description = "产品code：全部是-1")
    private String productCode;

    @Schema(description = "设备code:全部-1")
    private String deviceCode;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}