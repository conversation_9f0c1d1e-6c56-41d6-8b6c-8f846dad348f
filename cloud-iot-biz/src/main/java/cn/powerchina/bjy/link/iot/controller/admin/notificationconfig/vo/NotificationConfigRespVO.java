package cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 消息配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class NotificationConfigRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7663")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "配置名称", example = "赵六")
    @ExcelProperty("配置名称")
    private String notificationName;

    @Schema(description = "消息类型（1:钉钉 2：邮件 3：短信）")
    @ExcelProperty("消息类型（1:钉钉 2：邮件 3：短信）")
    private Integer notificationMethod;

    @Schema(description = "服务器地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("服务器地址")
    private String notificationHost;

    @Schema(description = "端口")
    @ExcelProperty("端口")
    private String notificationPort;

    @Schema(description = "授权码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("授权码")
    private String notificationPass;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("用户名")
    private String username;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("密码")
    private String password;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

}