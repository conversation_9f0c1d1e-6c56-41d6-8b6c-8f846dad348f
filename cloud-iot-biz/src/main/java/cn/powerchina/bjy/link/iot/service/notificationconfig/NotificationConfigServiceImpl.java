package cn.powerchina.bjy.link.iot.service.notificationconfig;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo.NotificationConfigPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo.NotificationConfigSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationconfig.NotificationConfigDO;
import cn.powerchina.bjy.link.iot.dal.mysql.notificationconfig.NotificationConfigMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.NOTIFICATION_CONFIG_NOT_EXISTS;

/**
 * 消息配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NotificationConfigServiceImpl implements NotificationConfigService {

    @Resource
    private NotificationConfigMapper notificationConfigMapper;

    @Override
    public Long createNotificationConfig(NotificationConfigSaveReqVO createReqVO) {
        // 插入
        NotificationConfigDO notificationConfig = BeanUtils.toBean(createReqVO, NotificationConfigDO.class);
        notificationConfigMapper.insert(notificationConfig);
        // 返回
        return notificationConfig.getId();
    }

    @Override
    public void updateNotificationConfig(NotificationConfigSaveReqVO updateReqVO) {
        // 校验存在
        validateNotificationConfigExists(updateReqVO.getId());
        // 更新
        NotificationConfigDO updateObj = BeanUtils.toBean(updateReqVO, NotificationConfigDO.class);
        notificationConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteNotificationConfig(Long id) {
        // 校验存在
        validateNotificationConfigExists(id);
        // 删除
        notificationConfigMapper.deleteById(id);
    }

    private void validateNotificationConfigExists(Long id) {
        if (notificationConfigMapper.selectById(id) == null) {
            throw exception(NOTIFICATION_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public NotificationConfigDO getNotificationConfig(Long id) {
        return notificationConfigMapper.selectById(id);
    }

    @Override
    public PageResult<NotificationConfigDO> getNotificationConfigPage(NotificationConfigPageReqVO pageReqVO) {
        return notificationConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public List<NotificationConfigDO> getNotificationConfigList() {
        return notificationConfigMapper.selectList();
    }

}