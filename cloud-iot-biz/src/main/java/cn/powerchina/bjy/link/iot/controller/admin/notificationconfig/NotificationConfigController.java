package cn.powerchina.bjy.link.iot.controller.admin.notificationconfig;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo.NotificationConfigPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo.NotificationConfigRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo.NotificationConfigSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationconfig.NotificationConfigDO;
import cn.powerchina.bjy.link.iot.service.notificationconfig.NotificationConfigService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 消息配置")
@RestController
@RequestMapping("/iot/notification-config")
@Validated
public class NotificationConfigController {

    @Resource
    private NotificationConfigService notificationConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建消息配置")
    @PreAuthorize("@ss.hasPermission('iot:notification-config:create')")
    public CommonResult<Long> createNotificationConfig(@Valid @RequestBody NotificationConfigSaveReqVO createReqVO) {
        return success(notificationConfigService.createNotificationConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新消息配置")
    @PreAuthorize("@ss.hasPermission('iot:notification-config:update')")
    public CommonResult<Boolean> updateNotificationConfig(@Valid @RequestBody NotificationConfigSaveReqVO updateReqVO) {
        notificationConfigService.updateNotificationConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除消息配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:notification-config:delete')")
    public CommonResult<Boolean> deleteNotificationConfig(@RequestParam("id") Long id) {
        notificationConfigService.deleteNotificationConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得消息配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:notification-config:query')")
    public CommonResult<NotificationConfigRespVO> getNotificationConfig(@RequestParam("id") Long id) {
        NotificationConfigDO notificationConfig = notificationConfigService.getNotificationConfig(id);
        return success(BeanUtils.toBean(notificationConfig, NotificationConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得消息配置分页")
    @PreAuthorize("@ss.hasPermission('iot:notification-config:query')")
    public CommonResult<PageResult<NotificationConfigRespVO>> getNotificationConfigPage(@Valid NotificationConfigPageReqVO pageReqVO) {
        PageResult<NotificationConfigDO> pageResult = notificationConfigService.getNotificationConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NotificationConfigRespVO.class));
    }

}