package cn.powerchina.bjy.link.iot.controller.admin.transportsource;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsTreeVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.TransportSourcePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.TransportSourceRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.TransportSourceSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportsource.TransportSourceDO;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import cn.powerchina.bjy.link.iot.service.transportsource.TransportSourceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 转发规则-数据源")
@RestController
@RequestMapping("/iot/transport-source")
@Validated
public class TransportSourceController {

    @Resource
    private TransportSourceService transportSourceService;

    @PostMapping("/create")
    @Operation(summary = "创建转发规则-数据源")
    @PreAuthorize("@ss.hasPermission('iot:transport-source:create')")
    public CommonResult<Long> createTransportSource(@Valid @RequestBody TransportSourceSaveReqVO createReqVO) {
        return success(transportSourceService.createTransportSource(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新转发规则-数据源")
    @PreAuthorize("@ss.hasPermission('iot:transport-source:update')")
    public CommonResult<Boolean> updateTransportSource(@Valid @RequestBody TransportSourceSaveReqVO updateReqVO) {
        transportSourceService.updateTransportSource(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除转发规则-数据源")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:transport-source:delete')")
    public CommonResult<Boolean> deleteTransportSource(@RequestParam("id") Long id) {
        transportSourceService.deleteTransportSource(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得转发规则-数据源")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:transport-source:query')")
    public CommonResult<TransportSourceRespVO> getTransportSource(@RequestParam("id") Long id) {
        TransportSourceDO transportSource = transportSourceService.getTransportSource(id);
        return success(BeanUtils.toBean(transportSource, TransportSourceRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得转发规则-数据源分页")
    @PreAuthorize("@ss.hasPermission('iot:transport-source:query')")
    public CommonResult<PageResult<TransportSourceRespVO>> getTransportSourcePage(@Valid TransportSourcePageReqVO pageReqVO) {
        PageResult<TransportSourceDO> pageResult = transportSourceService.getTransportSourcePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransportSourceRespVO.class));
    }

    @GetMapping("/getSourceTypeList")
    @Operation(summary = "获得转发规则-数据源数据类型")
    public CommonResult<List<Map<String, Object>>> getSourceTypeList() {
        return success(TransportSourceTypeEnum.getEnumList());
    }

    @GetMapping("/getRange")
    @Operation(summary = "获取数据范围", description = "获取数据范围")
    public CommonResult<List<DataPermissionsTreeVO>> getRange() {
        List<DataPermissionsTreeVO> list = transportSourceService.getRange();
        return success(list);
    }


}