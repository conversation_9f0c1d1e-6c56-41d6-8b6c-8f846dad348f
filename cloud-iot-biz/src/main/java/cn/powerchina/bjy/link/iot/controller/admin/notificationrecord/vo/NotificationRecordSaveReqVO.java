package cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 消息记录新增/修改 Request VO")
@Data
public class NotificationRecordSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27681")
    private Long id;

    @Schema(description = "告警id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long alarmId;

    @Schema(description = "主题")
    private String subject;

    @Schema(description = "收件箱")
    private String inbox;

    @Schema(description = "发送状态，1成功 0失败", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "发送状态，1成功 0失败不能为空")
    private Boolean sendStatus;

    @Schema(description = "发件箱")
    private String outbox;

    @Schema(description = "发送时间")
    private LocalDateTime sendTime;

    @Schema(description = "邮件内容")
    private String emailContent;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}