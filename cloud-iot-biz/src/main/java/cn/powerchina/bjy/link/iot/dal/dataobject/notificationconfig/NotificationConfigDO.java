package cn.powerchina.bjy.link.iot.dal.dataobject.notificationconfig;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 消息配置 DO
 *
 * <AUTHOR>
 */
@TableName("iot_notification_config")
@KeySequence("iot_notification_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationConfigDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 配置名称
     */
    private String notificationName;
    /**
     * 消息类型（1:钉钉 2：邮件 3：短信）
     */
    private Integer notificationMethod;
    /**
     * 服务器地址
     */
    private String notificationHost;
    /**
     * 端口
     */
    private String notificationPort;
    /**
     * 授权码
     */
    private String notificationPass;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;

}