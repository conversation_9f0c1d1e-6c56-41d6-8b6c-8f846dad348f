package cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo.SceneAlarmRecordDetailPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo.SceneAlarmRecordDetailRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo.SceneAlarmRecordDetailSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecorddetail.SceneAlarmRecordDetailDO;
import cn.powerchina.bjy.link.iot.service.scenealarmrecorddetail.SceneAlarmRecordDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 告警记录详情")
@RestController
@RequestMapping("/iot/scene-alarm-record-detail")
@Validated
public class SceneAlarmRecordDetailController {

    @Resource
    private SceneAlarmRecordDetailService sceneAlarmRecordDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建告警记录详情")
    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record-detail:create')")
    public CommonResult<Long> createSceneAlarmRecordDetail(@Valid @RequestBody SceneAlarmRecordDetailSaveReqVO createReqVO) {
        return success(sceneAlarmRecordDetailService.createSceneAlarmRecordDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新告警记录详情")
    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record-detail:update')")
    public CommonResult<Boolean> updateSceneAlarmRecordDetail(@Valid @RequestBody SceneAlarmRecordDetailSaveReqVO updateReqVO) {
        sceneAlarmRecordDetailService.updateSceneAlarmRecordDetail(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除告警记录详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record-detail:delete')")
    public CommonResult<Boolean> deleteSceneAlarmRecordDetail(@RequestParam("id") Long id) {
        sceneAlarmRecordDetailService.deleteSceneAlarmRecordDetail(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得告警记录详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record-detail:query')")
    public CommonResult<SceneAlarmRecordDetailRespVO> getSceneAlarmRecordDetail(@RequestParam("id") Long id) {
        SceneAlarmRecordDetailDO sceneAlarmRecordDetail = sceneAlarmRecordDetailService.getSceneAlarmRecordDetail(id);
        return success(BeanUtils.toBean(sceneAlarmRecordDetail, SceneAlarmRecordDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得告警记录详情分页")
    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record-detail:query')")
    public CommonResult<PageResult<SceneAlarmRecordDetailRespVO>> getSceneAlarmRecordDetailPage(@Valid SceneAlarmRecordDetailPageReqVO pageReqVO) {
        PageResult<SceneAlarmRecordDetailDO> pageResult = sceneAlarmRecordDetailService.getSceneAlarmRecordDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SceneAlarmRecordDetailRespVO.class));
    }

}