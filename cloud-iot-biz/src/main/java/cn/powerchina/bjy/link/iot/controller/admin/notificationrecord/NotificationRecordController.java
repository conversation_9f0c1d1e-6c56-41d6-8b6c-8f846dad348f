package cn.powerchina.bjy.link.iot.controller.admin.notificationrecord;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationrecord.NotificationRecordDO;
import cn.powerchina.bjy.link.iot.service.notificationrecord.NotificationRecordService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 消息记录")
@RestController
@RequestMapping("/iot/notification-record")
@Validated
public class NotificationRecordController {

    @Resource
    private NotificationRecordService notificationRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建消息记录")
    @PreAuthorize("@ss.hasPermission('iot:notification-record:create')")
    public CommonResult<Long> createNotificationRecord(@Valid @RequestBody NotificationRecordSaveReqVO createReqVO) {
        return success(notificationRecordService.createNotificationRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新消息记录")
    @PreAuthorize("@ss.hasPermission('iot:notification-record:update')")
    public CommonResult<Boolean> updateNotificationRecord(@Valid @RequestBody NotificationRecordSaveReqVO updateReqVO) {
        notificationRecordService.updateNotificationRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除消息记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:notification-record:delete')")
    public CommonResult<Boolean> deleteNotificationRecord(@RequestParam("id") Long id) {
        notificationRecordService.deleteNotificationRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得消息记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:notification-record:query')")
    public CommonResult<NotificationRecordRespVO> getNotificationRecord(@RequestParam("id") Long id) {
        NotificationRecordDO notificationRecord = notificationRecordService.getNotificationRecord(id);
        return success(BeanUtils.toBean(notificationRecord, NotificationRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得消息记录分页")
    @PreAuthorize("@ss.hasPermission('iot:notification-record:query')")
    public CommonResult<PageResult<NotificationRecordRespVO>> getNotificationRecordPage(@Valid NotificationRecordPageReqVO pageReqVO) {
        PageResult<NotificationRecordDO> pageResult = notificationRecordService.getNotificationRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NotificationRecordRespVO.class));
    }

}