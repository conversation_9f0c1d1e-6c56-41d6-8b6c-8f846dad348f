package cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 转发规则-转发目标 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TransportTargetRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2097")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "规则id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26611")
    @ExcelProperty("规则id")
    private Long ruleId;

    @Schema(description = "目标名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("目标名称")
    private String name;

    @Schema(description = "产品code", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品code")
    private String productCode;

    @Schema(description = "设备code", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设备code")
    private String deviceCode;

    @Schema(description = "转发类型(1:HTTP推送 2MQTT推送)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("转发类型(1:HTTP推送 2MQTT推送)")
    private Integer transportType;

    @Schema(description = "url地址")
    @ExcelProperty("url地址")
    private String urlAddress;

    @Schema(description = "token")
    @ExcelProperty("token")
    private String token;

    @Schema(description = "转发topic")
    @ExcelProperty("转发topic")
    private String topic;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}