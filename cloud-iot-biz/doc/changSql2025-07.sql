DROP TABLE iot_transport_rule;
CREATE TABLE iot_transport_rule
(
    id          bigint unsigned  auto_increment comment '主键id'
primary key,
    name        varchar(64)                           not null comment '规则名称',
    rule_code   varchar(64)                           not null comment '转发规则编码',
    status      tinyint     default '0'               not null comment '启用状态（0:未启动 1：运行中）',
    remark      varchar(255)                          null comment '规则描述',
    creator     varchar(64) default ''                null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     varchar(64) default ''                null comment '更新者',
    update_time datetime                              null comment '更新时间',
    deleted     bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '转发规则';


CREATE TABLE iot_transport_source
(
    id                bigint unsigned auto_increment comment '主键id'
        primary key,
    rule_id           bigint                                not null comment '规则id',
    data_type         varchar(255)                           not null comment '数据类型，逗号分隔',
    resource_space_id bigint                                null comment '资源空间id：全部是-1',
    product_code      varchar(64)                           not null comment '产品code：全部是-1',
    device_code       varchar(64)                           not null comment '设备code:全部-1',
    creator           varchar(64) default ''                null comment '创建者',
    create_time       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater           varchar(64) default ''                null comment '更新者',
    update_time       datetime                              null comment '更新时间',
    deleted           bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '转发规则-数据源';


CREATE TABLE iot_transport_target
(
    id             bigint unsigned auto_increment comment '主键id'
        primary key,
    rule_id        bigint                                not null comment '规则id',
    name           varchar(64)                           not null comment '目标名称',
    product_code   varchar(64)                           not null comment '产品code',
    device_code    varchar(64)                           not null comment '设备code',
    transport_type tinyint                               not null comment '转发类型(1:HTTP推送 2MQTT推送)',
    url_address    varchar(255)                          null comment 'url地址',
    token          varchar(255)                          null comment 'token',
    topic          varchar(255)                          null comment '转发topic',
    creator        varchar(64) default ''                null comment '创建者',
    create_time    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater        varchar(64) default ''                null comment '更新者',
    update_time    datetime                              null comment '更新时间',
    deleted        bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '转发规则-转发目标';


CREATE TABLE iot_app_auth
(
    id          bigint unsigned auto_increment comment '主键id'
        primary key,
    app_name    varchar(64)                           not null comment '应用名称',
    app_code    varchar(64)                           not null comment '应用code',
    secret      varchar(64)                           null comment '密钥',
    status      tinyint                               null comment '应用状态：启用/禁用',
    creator     varchar(64) default ''                null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     varchar(64) default ''                null comment '更新者',
    update_time datetime                              null comment '更新时间',
    deleted     bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '应用管理认证';


CREATE TABLE `iot_scene_alarm_record_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `alarm_id` bigint NOT NULL COMMENT '告警id',
  `rule_id` bigint NOT NULL COMMENT '规则ID',
  `alarm_time` datetime DEFAULT NULL COMMENT '告警时间',
  `device_name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '触发设备',
  `trigger_condition` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '触发条件',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，默认为0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='告警记录详情表';


CREATE TABLE `iot_notification_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `notification_name` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '配置名称',
  `notification_method` tinyint DEFAULT NULL COMMENT '消息类型（1:钉钉 2：邮件 3：短信）',
  `notification_host` varchar(500) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '服务器地址',
  `notification_port` varchar(20) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '端口',
  `notification_pass` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '授权码',
  `username` varchar(30) COLLATE utf8mb4_bin NOT NULL COMMENT '用户名',
  `password` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '密码',
  `creator` varchar(64) COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，默认为0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='消息配置';


CREATE TABLE `iot_notification_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `alarm_id` bigint NOT NULL COMMENT '告警id',
  `subject` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '主题',
  `inbox` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '收件箱',
  `send_status` bit(1) NOT NULL DEFAULT b'1' COMMENT '发送状态，1成功 0失败',
  `outbox` varchar(50) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '发件箱',
  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
  `email_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '邮件内容',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_bin DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，默认为0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='消息记录';